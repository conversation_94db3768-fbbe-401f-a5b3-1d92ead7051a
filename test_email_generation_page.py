#!/usr/bin/env python3
"""
邮箱生成页面测试脚本
验证修复后的页面是否可以正常启动和运行
"""

import sys
import os
sys.path.append('src')

def test_qml_syntax():
    """测试QML语法"""
    print("🔍 测试QML语法...")
    
    # 运行括号检查
    result = os.system('python3 check_brackets.py "src/views/qml/pages/EmailGenerationPage.qml"')
    if result == 0:
        print("✅ QML语法检查通过")
        return True
    else:
        print("❌ QML语法检查失败")
        return False

def test_responsive_layout():
    """测试响应式布局特性"""
    print("🔍 测试响应式布局特性...")
    
    qml_file = "src/views/qml/pages/EmailGenerationPage.qml"
    
    # 检查紧凑模式属性
    with open(qml_file, 'r', encoding='utf-8') as f:
        content = f.read()
        
    required_features = [
        'property bool isCompactMode',  # 紧凑模式属性
        'Layout.minimumWidth',  # 最小宽度设置
        'Layout.preferredWidth',  # 首选宽度设置
        'visible: !root.isCompactMode',  # 紧凑模式可见性控制
        'Math.max(',  # 动态宽度计算
    ]
    
    missing_features = []
    for feature in required_features:
        if feature not in content:
            missing_features.append(feature)
    
    if not missing_features:
        print("✅ 响应式布局特性检查通过")
        return True
    else:
        print(f"❌ 缺少响应式布局特性: {missing_features}")
        return False

def test_component_structure():
    """测试组件结构"""
    print("🔍 测试组件结构...")
    
    qml_file = "src/views/qml/pages/EmailGenerationPage.qml"
    
    with open(qml_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_components = [
        'Rectangle {',  # 根组件
        'RowLayout {',  # 主布局
        'ScrollView {',  # 滚动视图
        'TagSelector {',  # 标签选择器
        'Timer {',  # 安全定时器
        'ProgressBar {',  # 进度条
    ]
    
    missing_components = []
    for component in required_components:
        if component not in content:
            missing_components.append(component)
    
    if not missing_components:
        print("✅ 组件结构检查通过")
        return True
    else:
        print(f"❌ 缺少必要组件: {missing_components}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试邮箱生成页面...")
    print("=" * 50)
    
    tests = [
        test_qml_syntax,
        test_responsive_layout,
        test_component_structure,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试出错: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！邮箱生成页面修复成功")
        return True
    else:
        print("⚠️ 部分测试未通过，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)