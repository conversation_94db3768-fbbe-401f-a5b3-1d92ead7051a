/*
 * 邮箱申请页面 - 重新设计版本
 * 左侧：操作日志和状态信息
 * 右侧：邮箱生成功能区域（横向布局）
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls.Material 2.15
import "../components"

Rectangle {
    id: root
    color: "#f5f5f5"

    // 对外暴露的属性
    property bool isConfigured: false
    property string currentDomain: "未配置"
    property var statistics: ({})
    property var availableTags: []
    property bool isCompactMode: width < 1200  // 调整紧凑模式阈值

    // 对外暴露的信号
    signal statusChanged(string message)
    signal logMessage(string message)
    signal requestTagRefresh()
    signal createNewTag(string tagName)

    RowLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20

        // 左侧：操作日志和状态区域
        Rectangle {
            Layout.minimumWidth: 300
            Layout.preferredWidth: 350
            Layout.maximumWidth: 400
            Layout.fillHeight: true
            color: "white"
            radius: 12
            border.color: "#e0e0e0"
            border.width: 1

            // 添加阴影效果
            Rectangle {
                anchors.fill: parent
                anchors.margins: -3
                color: "#10000000"
                radius: parent.radius + 3
                z: -1
            }

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 16

                // 标题和状态指示器
                Row {
                    Layout.fillWidth: true
                    spacing: 12

                    Rectangle {
                        width: 8
                        height: 8
                        radius: 4
                        color: root.isConfigured ? "#4CAF50" : "#F44336"
                        anchors.verticalCenter: parent.verticalCenter

                        // 呼吸灯效果
                        SequentialAnimation on opacity {
                            running: true
                            loops: Animation.Infinite
                            NumberAnimation { to: 0.3; duration: 1500 }
                            NumberAnimation { to: 1.0; duration: 1500 }
                        }
                    }

                    Label {
                        text: "📝 操作日志"
                        font.bold: true
                        font.pixelSize: 18
                        color: "#333"
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }

                // 域名和统计信息卡片
                Rectangle {
                    Layout.fillWidth: true
                    height: 120
                    color: "#f8f9fa"
                    radius: 8
                    border.color: "#e9ecef"

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 12

                        // 域名信息
                        Row {
                            Layout.fillWidth: true
                            spacing: 8

                            Text {
                                text: "🌐"
                                font.pixelSize: 16
                                anchors.verticalCenter: parent.verticalCenter
                            }

                            Column {
                                anchors.verticalCenter: parent.verticalCenter
                                spacing: 2

                                Label {
                                    text: root.currentDomain
                                    font.pixelSize: 14
                                    font.weight: Font.Medium
                                    color: root.isConfigured ? "#4CAF50" : "#F44336"
                                }

                                Label {
                                    text: root.isConfigured ? "已配置" : "未配置"
                                    font.pixelSize: 11
                                    color: "#666"
                                }
                            }
                        }

                        Rectangle {
                            Layout.fillWidth: true
                            height: 1
                            color: "#e0e0e0"
                        }

                        // 统计信息
                        Flow {
                            Layout.fillWidth: true
                            spacing: 16

                            Column {
                                spacing: 2
                                Label {
                                    text: (root.statistics.total_emails || 0).toString()
                                    font.pixelSize: 16
                                    font.weight: Font.Bold
                                    color: "#2196F3"
                                }
                                Label {
                                    text: "总邮箱"
                                    font.pixelSize: 10
                                    color: "#666"
                                }
                            }

                            Column {
                                spacing: 2
                                Label {
                                    text: (root.statistics.today_created || 0).toString()
                                    font.pixelSize: 16
                                    font.weight: Font.Bold
                                    color: "#FF9800"
                                }
                                Label {
                                    text: "今日创建"
                                    font.pixelSize: 10
                                    color: "#666"
                                }
                            }

                            Column {
                                spacing: 2
                                Label {
                                    text: (root.statistics.success_rate || 100) + "%"
                                    font.pixelSize: 16
                                    font.weight: Font.Bold
                                    color: "#4CAF50"
                                }
                                Label {
                                    text: "成功率"
                                    font.pixelSize: 10
                                    color: "#666"
                                }
                            }
                        }
                    }
                }

                // 最新生成的邮箱
                Rectangle {
                    id: latestEmailCard
                    Layout.fillWidth: true
                    height: 70
                    color: "#e3f2fd"
                    radius: 8
                    border.color: "#2196F3"
                    border.width: 1
                    visible: latestEmailLabel.text !== ""

                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 12
                        spacing: 12

                        Rectangle {
                            width: 40
                            height: 40
                            radius: 20
                            color: "#2196F3"

                            Text {
                                anchors.centerIn: parent
                                text: "✉️"
                                font.pixelSize: 18
                                color: "white"
                            }
                        }

                        Column {
                            Layout.fillWidth: true
                            spacing: 4

                            Label {
                                text: "最新生成的邮箱"
                                font.pixelSize: 11
                                color: "#1976D2"
                                font.weight: Font.Medium
                            }

                            Label {
                                id: latestEmailLabel
                                text: ""
                                font.pixelSize: 12
                                color: "#1565C0"
                                font.weight: Font.Bold
                                elide: Text.ElideMiddle
                                width: parent.width

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        console.log("复制邮箱地址:", latestEmailLabel.text)
                                        root.logMessage("📋 邮箱地址已复制到剪贴板")
                                    }
                                    cursorShape: Qt.PointingHandCursor
                                }
                            }
                        }

                        Button {
                            text: "📋"
                            width: 32
                            height: 32
                            background: Rectangle {
                                color: parent.hovered ? "#1976D2" : "#2196F3"
                                radius: 16
                            }
                            contentItem: Text {
                                text: parent.text
                                color: "white"
                                horizontalAlignment: Text.AlignHCenter
                                verticalAlignment: Text.AlignVCenter
                            }
                            onClicked: {
                                console.log("复制邮箱:", latestEmailLabel.text)
                                root.logMessage("📋 邮箱地址已复制")
                            }
                        }
                    }
                }

                // 日志区域
                ScrollView {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    clip: true

                    ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                    ScrollBar.vertical.policy: ScrollBar.AsNeeded

                    TextArea {
                        id: logArea
                        readOnly: true
                        wrapMode: TextArea.Wrap
                        font.family: "Consolas, Monaco, monospace"
                        font.pixelSize: 11
                        color: "#333"
                        selectByMouse: true
                        text: "[" + new Date().toLocaleTimeString() + "] 邮箱生成页面已加载\n[" + new Date().toLocaleTimeString() + "] 等待用户操作..."

                        background: Rectangle {
                            color: "#fafafa"
                            radius: 6
                            border.color: "#e0e0e0"
                        }

                        function addLog(message) {
                            var timestamp = new Date().toLocaleTimeString()
                            text += "\n[" + timestamp + "] " + message
                            cursorPosition = length
                        }
                    }
                }
            }
        }

        // 右侧：邮箱生成功能区域
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "white"
            radius: 12
            border.color: "#e0e0e0"
            border.width: 1

            // 添加阴影效果
            Rectangle {
                anchors.fill: parent
                anchors.margins: -3
                color: "#10000000"
                radius: parent.radius + 3
                z: -1
            }

            ScrollView {
                anchors.fill: parent
                anchors.margins: 24
                clip: true

                ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                ScrollBar.vertical.policy: ScrollBar.AsNeeded

                ColumnLayout {
                    width: parent.width
                    spacing: 24

                    // 页面标题
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 16

                        Rectangle {
                            width: 48
                            height: 48
                            radius: 24
                            gradient: Gradient {
                                GradientStop { position: 0.0; color: "#42A5F5" }
                                GradientStop { position: 1.0; color: "#1976D2" }
                            }

                            Text {
                                anchors.centerIn: parent
                                text: "🎯"
                                font.pixelSize: 24
                                color: "white"
                            }
                        }

                        Column {
                            Layout.fillWidth: true
                            spacing: 4

                            Label {
                                text: "邮箱生成器"
                                font.bold: true
                                font.pixelSize: 24
                                color: "#333"
                            }

                            Label {
                                text: "配置生成参数，快速创建邮箱地址"
                                font.pixelSize: 14
                                color: "#666"
                            }
                        }

                        // 紧凑模式下的状态信息
                        Rectangle {
                            Layout.preferredWidth: 200
                            height: 48
                            color: "#f8f9fa"
                            radius: 8
                            border.color: "#e9ecef"
                            visible: root.isCompactMode

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8
                                spacing: 8

                                Rectangle {
                                    width: 6
                                    height: 6
                                    radius: 3
                                    color: root.isConfigured ? "#4CAF50" : "#F44336"
                                }

                                Column {
                                    Layout.fillWidth: true
                                    spacing: 2

                                    Label {
                                        text: root.currentDomain
                                        font.pixelSize: 11
                                        font.weight: Font.Medium
                                        color: root.isConfigured ? "#4CAF50" : "#F44336"
                                        elide: Text.ElideRight
                                        width: parent.width
                                    }

                                    Label {
                                        text: "总数: " + (root.statistics.total_emails || 0) + " | 今日: " + (root.statistics.today_created || 0)
                                        font.pixelSize: 10
                                        color: "#666"
                                    }
                                }
                            }
                        }
                    }

                    // 主要配置区域 - 横向布局
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 20
                        alignment: Qt.AlignTop

                        // 左列：生成模式和前缀设置
                        ColumnLayout {
                            Layout.preferredWidth: parent.width * 0.4
                            Layout.alignment: Qt.AlignTop
                            spacing: 16

                            // 生成模式选择
                            GroupBox {
                                Layout.fillWidth: true
                                title: "生成模式"
                                font.pixelSize: 14
                                font.weight: Font.Medium

                                background: Rectangle {
                                    color: "#fafafa"
                                    radius: 8
                                    border.color: "#e0e0e0"
                                }

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 8

                                    ButtonGroup {
                                        id: prefixTypeGroup
                                    }

                                    RadioButton {
                                        id: randomNameRadio
                                        text: "随机名字"
                                        checked: true
                                        ButtonGroup.group: prefixTypeGroup
                                        font.pixelSize: 13

                                        indicator: Rectangle {
                                            implicitWidth: 18
                                            implicitHeight: 18
                                            x: randomNameRadio.leftPadding
                                            y: parent.height / 2 - height / 2
                                            radius: 9
                                            border.color: randomNameRadio.checked ? "#2196F3" : "#ccc"
                                            border.width: 2

                                            Rectangle {
                                                width: 8
                                                height: 8
                                                x: 5
                                                y: 5
                                                radius: 4
                                                color: "#2196F3"
                                                visible: randomNameRadio.checked
                                            }
                                        }
                                    }

                                    Label {
                                        text: "例：<EMAIL>"
                                        font.pixelSize: 11
                                        color: "#999"
                                        leftPadding: 30
                                    }

                                    RadioButton {
                                        id: randomStringRadio
                                        text: "随机字符串"
                                        ButtonGroup.group: prefixTypeGroup
                                        font.pixelSize: 13

                                        indicator: Rectangle {
                                            implicitWidth: 18
                                            implicitHeight: 18
                                            x: randomStringRadio.leftPadding
                                            y: parent.height / 2 - height / 2
                                            radius: 9
                                            border.color: randomStringRadio.checked ? "#2196F3" : "#ccc"
                                            border.width: 2

                                            Rectangle {
                                                width: 8
                                                height: 8
                                                x: 5
                                                y: 5
                                                radius: 4
                                                color: "#2196F3"
                                                visible: randomStringRadio.checked
                                            }
                                        }
                                    }

                                    Label {
                                        text: "例：<EMAIL>"
                                        font.pixelSize: 11
                                        color: "#999"
                                        leftPadding: 30
                                    }

                                    RadioButton {
                                        id: customPrefixRadio
                                        text: "自定义前缀"
                                        ButtonGroup.group: prefixTypeGroup
                                        font.pixelSize: 13

                                        indicator: Rectangle {
                                            implicitWidth: 18
                                            implicitHeight: 18
                                            x: customPrefixRadio.leftPadding
                                            y: parent.height / 2 - height / 2
                                            radius: 9
                                            border.color: customPrefixRadio.checked ? "#2196F3" : "#ccc"
                                            border.width: 2

                                            Rectangle {
                                                width: 8
                                                height: 8
                                                x: 5
                                                y: 5
                                                radius: 4
                                                color: "#2196F3"
                                                visible: customPrefixRadio.checked
                                            }
                                        }
                                    }

                                    // 自定义前缀输入
                                    Rectangle {
                                        Layout.fillWidth: true
                                        height: 40
                                        color: customPrefixRadio.checked ? "white" : "#f5f5f5"
                                        radius: 6
                                        border.color: customPrefixField.activeFocus ? "#2196F3" : "#e0e0e0"
                                        border.width: customPrefixField.activeFocus ? 2 : 1
                                        visible: customPrefixRadio.checked

                                        RowLayout {
                                            anchors.fill: parent
                                            anchors.margins: 8
                                            spacing: 8

                                            Text {
                                                text: "📝"
                                                font.pixelSize: 14
                                                color: "#666"
                                            }

                                            TextField {
                                                id: customPrefixField
                                                Layout.fillWidth: true
                                                placeholderText: "输入自定义前缀..."
                                                enabled: customPrefixRadio.checked
                                                font.pixelSize: 13
                                                background: Item {}
                                                selectByMouse: true
                                                color: "#333"
                                            }
                                        }
                                    }
                                }
                            }

                            // 批量生成选项
                            GroupBox {
                                Layout.fillWidth: true
                                title: "生成选项"
                                font.pixelSize: 14
                                font.weight: Font.Medium

                                background: Rectangle {
                                    color: "#fafafa"
                                    radius: 8
                                    border.color: "#e0e0e0"
                                }

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 12

                                    CheckBox {
                                        id: batchModeCheckBox
                                        text: "批量生成模式"
                                        font.pixelSize: 13

                                        indicator: Rectangle {
                                            implicitWidth: 18
                                            implicitHeight: 18
                                            x: batchModeCheckBox.leftPadding
                                            y: parent.height / 2 - height / 2
                                            radius: 3
                                            border.color: batchModeCheckBox.checked ? "#2196F3" : "#ccc"
                                            border.width: 2
                                            color: batchModeCheckBox.checked ? "#2196F3" : "transparent"

                                            Text {
                                                anchors.centerIn: parent
                                                text: "✓"
                                                color: "white"
                                                font.pixelSize: 10
                                                font.bold: true
                                                visible: batchModeCheckBox.checked
                                            }
                                        }

                                        onCheckedChanged: {
                                            if (checked) {
                                                batchCountSpinBox.focus = true
                                            }
                                        }
                                    }

                                    Rectangle {
                                        Layout.fillWidth: true
                                        height: 40
                                        color: batchModeCheckBox.checked ? "white" : "#f5f5f5"
                                        radius: 6
                                        border.color: batchModeCheckBox.checked ? "#e0e0e0" : "transparent"
                                        border.width: 1
                                        visible: batchModeCheckBox.checked

                                        RowLayout {
                                            anchors.fill: parent
                                            anchors.margins: 8
                                            spacing: 12

                                            Text {
                                                text: "🔢"
                                                font.pixelSize: 14
                                                color: "#666"
                                            }

                                            Label {
                                                text: "生成数量:"
                                                font.pixelSize: 13
                                                color: "#333"
                                            }

                                            SpinBox {
                                                id: batchCountSpinBox
                                                from: 1
                                                to: 50
                                                value: 5
                                                enabled: batchModeCheckBox.checked
                                                implicitWidth: 100

                                                background: Rectangle {
                                                    color: "#f8f9fa"
                                                    radius: 4
                                                    border.color: "#e0e0e0"
                                                }
                                            }

                                            Label {
                                                text: "个"
                                                font.pixelSize: 13
                                                color: "#666"
                                            }

                                            Item { Layout.fillWidth: true }
                                        }
                                    }

                                    Rectangle {
                                        Layout.fillWidth: true
                                        height: 32
                                        color: "#fff3e0"
                                        radius: 6
                                        border.color: "#ffcc02"
                                        border.width: 1
                                        visible: batchModeCheckBox.checked

                                        RowLayout {
                                            anchors.fill: parent
                                            anchors.margins: 8
                                            spacing: 8

                                            Text {
                                                text: "💡"
                                                font.pixelSize: 12
                                                color: "#f57c00"
                                            }

                                            Label {
                                                Layout.fillWidth: true
                                                text: "批量模式将同时生成多个邮箱"
                                                font.pixelSize: 11
                                                color: "#f57c00"
                                                wrapMode: Text.WordWrap
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // 右列：标签设置和备注
                        ColumnLayout {
                            Layout.fillWidth: true
                            Layout.alignment: Qt.AlignTop
                            spacing: 16

                            // 标签选择器
                            GroupBox {
                                Layout.fillWidth: true
                                title: "标签设置"
                                font.pixelSize: 14
                                font.weight: Font.Medium

                                background: Rectangle {
                                    color: "#fafafa"
                                    radius: 8
                                    border.color: "#e0e0e0"
                                }

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 12

                                    Label {
                                        text: "为生成的邮箱添加标签，便于分类管理"
                                        font.pixelSize: 12
                                        color: "#666"
                                        wrapMode: Text.WordWrap
                                        Layout.fillWidth: true
                                    }

                                    TagSelector {
                                        id: tagSelector
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 120
                                        maxHeight: 150

                                        availableTags: root.availableTags
                                        placeholderText: "搜索标签或输入新标签名称..."
                                        allowCreateNew: true

                                        onNewTagRequested: function(tagName) {
                                            console.log("请求创建新标签:", tagName)
                                            root.createNewTag(tagName)
                                        }

                                        onTagsChanged: function(selectedTags) {
                                            console.log("选中的标签已变更:", selectedTags.length)
                                        }
                                    }

                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 8

                                        Button {
                                            text: "🔄 刷新标签"
                                            font.pixelSize: 11
                                            implicitHeight: 28
                                            flat: true
                                            onClicked: root.requestTagRefresh()

                                            background: Rectangle {
                                                color: parent.hovered ? "#f0f0f0" : "transparent"
                                                radius: 4
                                            }
                                        }

                                        Item { Layout.fillWidth: true }

                                        Label {
                                            text: "已选择 " + tagSelector.selectedTags.length + " 个标签"
                                            font.pixelSize: 11
                                            color: "#666"
                                        }
                                    }
                                }
                            }

                            // 备注输入
                            GroupBox {
                                Layout.fillWidth: true
                                title: "备注信息"
                                font.pixelSize: 14
                                font.weight: Font.Medium

                                background: Rectangle {
                                    color: "#fafafa"
                                    radius: 8
                                    border.color: "#e0e0e0"
                                }

                                Rectangle {
                                    anchors.fill: parent
                                    color: "white"
                                    radius: 6
                                    border.color: notesField.activeFocus ? "#2196F3" : "#e0e0e0"
                                    border.width: notesField.activeFocus ? 2 : 1

                                    RowLayout {
                                        anchors.fill: parent
                                        anchors.margins: 12
                                        spacing: 8

                                        Text {
                                            text: "💭"
                                            font.pixelSize: 14
                                            color: "#666"
                                        }

                                        TextField {
                                            id: notesField
                                            Layout.fillWidth: true
                                            placeholderText: "为邮箱添加备注说明（可选）..."
                                            font.pixelSize: 13
                                            background: Item {}
                                            selectByMouse: true
                                            color: "#333"
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 生成按钮和进度指示器
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 16

                        // 进度条
                        ProgressBar {
                            id: progressBar
                            Layout.fillWidth: true
                            value: 0
                            visible: value > 0

                            background: Rectangle {
                                implicitWidth: 200
                                implicitHeight: 6
                                color: "#e0e0e0"
                                radius: 3
                            }

                            contentItem: Item {
                                implicitWidth: 200
                                implicitHeight: 6

                                Rectangle {
                                    width: progressBar.visualPosition * parent.width
                                    height: parent.height
                                    radius: 3
                                    color: "#2196F3"
                                }
                            }
                        }

                        // 生成按钮
                        Rectangle {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 56
                            radius: 12

                            gradient: Gradient {
                                GradientStop {
                                    position: 0.0
                                    color: generateButton.enabled ? "#42A5F5" : "#e0e0e0"
                                }
                                GradientStop {
                                    position: 1.0
                                    color: generateButton.enabled ? "#1976D2" : "#bdbdbd"
                                }
                            }

                            // 阴影效果
                            Rectangle {
                                anchors.fill: parent
                                anchors.margins: -2
                                color: "#20000000"
                                radius: parent.radius + 2
                                z: -1
                                y: 2
                                visible: generateButton.enabled
                            }

                            Button {
                                id: generateButton
                                anchors.fill: parent
                                text: {
                                    if (isGenerating) {
                                        return "🔄 生成中..."
                                    } else if (batchModeCheckBox.checked) {
                                        return "🎯 批量生成 " + batchCountSpinBox.value + " 个邮箱"
                                    } else {
                                        return "🎯 生成新邮箱"
                                    }
                                }
                                font.pixelSize: 16
                                font.weight: Font.Medium
                                enabled: root.isConfigured && !isGenerating

                                background: Rectangle {
                                    color: "transparent"
                                    radius: 12
                                }

                                contentItem: Text {
                                    text: parent.text
                                    font: parent.font
                                    color: parent.enabled ? "white" : "#999"
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }

                                property bool isGenerating: false

                                onClicked: {
                                    if (!validateInput()) {
                                        return
                                    }

                                    isGenerating = true
                                    safetyTimer.restart()

                                    var prefixType = "random_name"
                                    if (randomStringRadio.checked) prefixType = "random_string"
                                    else if (customPrefixRadio.checked) prefixType = "custom"

                                    var selectedTagIds = tagSelector.getSelectedTagIds()
                                    var selectedTagNames = tagSelector.getSelectedTagNames()

                                    console.log("生成邮箱 - 选中标签:", selectedTagNames)

                                    if (emailController) {
                                        try {
                                            if (batchModeCheckBox.checked) {
                                                addLogMessage("🔄 开始批量生成 " + batchCountSpinBox.value + " 个邮箱...")
                                                if (selectedTagNames.length > 0) {
                                                    addLogMessage("📌 标签: " + selectedTagNames.join(", "))
                                                }
                                                emailController.batchGenerateEmails(
                                                    batchCountSpinBox.value,
                                                    prefixType,
                                                    customPrefixField.text,
                                                    selectedTagIds,
                                                    notesField.text
                                                )
                                            } else {
                                                addLogMessage("🔄 开始生成邮箱...")
                                                if (selectedTagNames.length > 0) {
                                                    addLogMessage("📌 标签: " + selectedTagNames.join(", "))
                                                }
                                                emailController.generateCustomEmail(
                                                    prefixType,
                                                    customPrefixField.text,
                                                    selectedTagIds,
                                                    notesField.text
                                                )
                                            }
                                        } catch (e) {
                                            console.error("生成邮箱时发生错误:", e)
                                            addLogMessage("❌ 生成邮箱时发生错误: " + e)
                                            isGenerating = false
                                        }
                                    } else {
                                        console.error("emailController未初始化")
                                        addLogMessage("❌ 系统错误: 控制器未初始化")
                                        isGenerating = false
                                    }
                                }
                            }

                            // 安全定时器
                            Timer {
                                id: safetyTimer
                                interval: 30000
                                running: generateButton.isGenerating
                                repeat: false
                                onTriggered: {
                                    if (generateButton.isGenerating) {
                                        console.log("安全定时器触发：重置生成按钮状态")
                                        generateButton.isGenerating = false
                                        addLogMessage("⚠️ 生成操作超时，已重置按钮状态")
                                    }
                                }
                            }
                        }

                        // 快速操作按钮
                        RowLayout {
                            Layout.fillWidth: true
                            spacing: 12
                            visible: !generateButton.isGenerating

                            Button {
                                text: "📧 测试邮箱"
                                font.pixelSize: 12
                                implicitHeight: 36
                                Layout.fillWidth: true
                                enabled: latestEmailLabel.text !== ""

                                background: Rectangle {
                                    color: parent.hovered ? "#f0f0f0" : "#f8f9fa"
                                    radius: 6
                                    border.color: "#e0e0e0"
                                }

                                onClicked: {
                                    if (emailController && latestEmailLabel.text) {
                                        emailController.getVerificationCode(latestEmailLabel.text)
                                    }
                                }
                            }

                            Button {
                                text: "📋 复制地址"
                                font.pixelSize: 12
                                implicitHeight: 36
                                Layout.fillWidth: true
                                enabled: latestEmailLabel.text !== ""

                                background: Rectangle {
                                    color: parent.hovered ? "#f0f0f0" : "#f8f9fa"
                                    radius: 6
                                    border.color: "#e0e0e0"
                                }

                                onClicked: {
                                    console.log("复制邮箱:", latestEmailLabel.text)
                                    root.logMessage("📋 邮箱地址已复制")
                                }
                            }

                            Button {
                                text: "🔄 重置表单"
                                font.pixelSize: 12
                                implicitHeight: 36
                                Layout.fillWidth: true

                                background: Rectangle {
                                    color: parent.hovered ? "#fff3e0" : "#fff8e1"
                                    radius: 6
                                    border.color: "#ffcc02"
                                }

                                onClicked: clearInputs()
                            }
                        }
                    }

                    Item { Layout.fillHeight: true }
                }
            }
        }
    }

    Component.onCompleted: {
        console.log("邮箱生成页面已初始化")
        root.requestTagRefresh()
        addLogMessage("🔄 正在加载标签列表...")
    }

    // 内部方法
    function updateLatestEmail(email) {
        latestEmailLabel.text = email
    }

    function updateProgress(value) {
        progressBar.value = value / 100.0
    }

    function addLogMessage(message) {
        logArea.addLog(message)
    }

    function enableGenerateButton() {
        generateButton.isGenerating = false
        safetyTimer.stop()
        addLogMessage("✅ 生成操作完成，按钮已重新启用")
    }

    function disableGenerateButton() {
        generateButton.isGenerating = true
        addLogMessage("🔒 生成按钮已禁用")
    }

    function refreshTags() {
        console.log("刷新标签列表")
        root.requestTagRefresh()
        addLogMessage("🔄 正在刷新标签列表...")
    }

    function handleNewTag(tagName) {
        console.log("处理新标签创建请求:", tagName)
        root.createNewTag(tagName)
        addLogMessage("📝 正在创建新标签: " + tagName)
    }

    function onTagCreated(tag) {
        console.log("新标签已创建:", tag.name)
        addLogMessage("✅ 标签创建成功: " + tag.name)
        if (tagSelector) {
            tagSelector.selectTagById(tag.id)
        }
    }

    function onTagsLoaded(tags) {
        console.log("标签列表已加载，数量:", tags.length)
        root.availableTags = tags
        addLogMessage("✅ 标签列表已加载，共 " + tags.length + " 个标签")
    }

    function handleBatchResult(result) {
        if (result.success > 0) {
            addLogMessage("✅ 批量生成成功: " + result.success + " 个邮箱")
            if (result.emails && result.emails.length > 0) {
                updateLatestEmail(result.emails[0].email_address)
            }
        }
        if (result.failed > 0) {
            addLogMessage("❌ 生成失败: " + result.failed + " 个邮箱")
        }
        if (result.errors && result.errors.length > 0) {
            result.errors.forEach(function(error) {
                addLogMessage("❌ 错误: " + error)
            })
        }
    }

    function handleBatchProgress(current, total, message) {
        var percentage = Math.round((current / total) * 100)
        updateProgress(percentage)
        addLogMessage("📊 进度: " + current + "/" + total + " (" + percentage + "%) - " + message)
    }

    function validateInput() {
        if (!root.isConfigured) {
            addLogMessage("❌ 请先完成域名配置")
            return false
        }

        if (customPrefixRadio.checked && customPrefixField.text.trim().length === 0) {
            addLogMessage("❌ 请输入自定义前缀")
            return false
        }

        return true
    }

    function clearInputs() {
        customPrefixField.text = ""
        notesField.text = ""
        randomNameRadio.checked = true
        batchModeCheckBox.checked = false
        if (tagSelector) {
            tagSelector.clearSelection()
        }
        addLogMessage("🧹 输入字段已清空")
    }
}